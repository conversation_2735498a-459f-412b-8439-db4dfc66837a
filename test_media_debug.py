#!/usr/bin/env python3
"""
Script per testare e debuggare il calcolo della media giornaliera
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.database_pg import Database

def test_media_calculation():
    """Testa il calcolo della media giornaliera per tutti i cantieri"""
    
    try:
        db = Database()
        
        # Ottieni tutti i cantieri
        cantieri = db.execute_query("""
            SELECT id_cantiere, nome 
            FROM cantieri 
            ORDER BY id_cantiere
        """, fetch_all=True)
        
        print("=== TEST CALCOLO MEDIA GIORNALIERA ===\n")
        
        for cantiere in cantieri:
            cantiere_id = cantiere[0]
            nome_cantiere = cantiere[1]
            
            print(f"🏗️ Cantiere: {nome_cantiere} (ID: {cantiere_id})")
            print("-" * 50)
            
            # Test 1: Verifica dati generali
            stats = db.execute_query("""
                SELECT
                    COUNT(*) as totale_cavi,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN 1 ELSE 0 END) as cavi_installati,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_posati,
                    SUM(metri_teorici) as metri_teorici_totali
                FROM cavi
                WHERE id_cantiere = %s
            """, (cantiere_id,), fetch_one=True)
            
            print(f"📊 Statistiche generali:")
            print(f"   - Totale cavi: {stats[0]}")
            print(f"   - Cavi installati: {stats[1]}")
            print(f"   - Metri posati: {stats[2] or 0}")
            print(f"   - Metri teorici totali: {stats[3] or 0}")
            
            # Test 2: Verifica campi data
            date_check = db.execute_query("""
                SELECT
                    COUNT(*) as totale,
                    SUM(CASE WHEN data_posa IS NOT NULL THEN 1 ELSE 0 END) as con_data_posa,
                    SUM(CASE WHEN timestamp IS NOT NULL THEN 1 ELSE 0 END) as con_timestamp
                FROM cavi
                WHERE id_cantiere = %s AND stato_installazione = 'Installato'
            """, (cantiere_id,), fetch_one=True)
            
            print(f"📅 Verifica campi data (cavi installati):")
            print(f"   - Totale cavi installati: {date_check[0]}")
            print(f"   - Con data_posa: {date_check[1]}")
            print(f"   - Con timestamp: {date_check[2]}")
            
            # Test 3: Query originale (con timestamp)
            posa_timestamp = db.execute_query("""
                SELECT timestamp::date as data_posa, SUM(metratura_reale) as metri_posati
                FROM cavi
                WHERE id_cantiere = %s AND stato_installazione = 'Installato'
                    AND timestamp IS NOT NULL AND metratura_reale > 0
                GROUP BY timestamp::date
                ORDER BY timestamp::date DESC
                LIMIT 5
            """, (cantiere_id,), fetch_all=True)
            
            print(f"🕐 Query con timestamp (ultimi 5 giorni):")
            if posa_timestamp:
                for row in posa_timestamp:
                    print(f"   - {row[0]}: {row[1]} metri")
                media_timestamp = sum(float(row[1]) for row in posa_timestamp) / len(posa_timestamp)
                print(f"   📈 Media con timestamp: {media_timestamp:.2f} m/giorno")
            else:
                print("   - Nessun dato trovato")
            
            # Test 4: Query corretta (con data_posa)
            posa_data_posa = db.execute_query("""
                SELECT data_posa, SUM(metratura_reale) as metri_posati
                FROM cavi
                WHERE id_cantiere = %s AND stato_installazione = 'Installato'
                    AND data_posa IS NOT NULL AND metratura_reale > 0
                GROUP BY data_posa
                ORDER BY data_posa DESC
                LIMIT 5
            """, (cantiere_id,), fetch_all=True)
            
            print(f"📅 Query con data_posa (ultimi 5 giorni):")
            if posa_data_posa:
                for row in posa_data_posa:
                    print(f"   - {row[0]}: {row[1]} metri")
                media_data_posa = sum(float(row[1]) for row in posa_data_posa) / len(posa_data_posa)
                print(f"   📈 Media con data_posa: {media_data_posa:.2f} m/giorno")
            else:
                print("   - Nessun dato trovato")
            
            # Test 5: Verifica esempi di dati
            sample_data = db.execute_query("""
                SELECT id_cavo, stato_installazione, metratura_reale, data_posa, timestamp
                FROM cavi
                WHERE id_cantiere = %s
                ORDER BY timestamp DESC
                LIMIT 3
            """, (cantiere_id,), fetch_all=True)
            
            print(f"🔍 Esempi di dati (ultimi 3 cavi):")
            for row in sample_data:
                print(f"   - {row[0]}: stato={row[1]}, metri={row[2]}, data_posa={row[3]}, timestamp={row[4]}")
            
            print("\n" + "="*70 + "\n")
            
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_media_calculation()
